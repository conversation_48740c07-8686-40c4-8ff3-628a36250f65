<template>
	<div class="content-box">
		<div class="content">
			<u-sticky>
				<div class="search-box">
					<u-search @change="searchListFn" placeholder="请输入车位名称" :show-action="false"
						v-model="queryParams.shopName" shape="square" borderColor="#c4bebe" bgColor="#fff"
						:clearabled="true"></u-search>
				</div>
			</u-sticky>
			<mescroll-body :up="upOption" :down="upOption" ref="mescrollRef" @init="mescrollInit" @down="downCallback"
				@up="upCallback">
				<div class="list-item" v-for="(item, index) in dataList" :key="index">
					<div class="flex">
						<div class="first">车位编码：{{item.parkingCode}}</div>
						<div class="">
							<u-tag v-if="item.parkingState === '0'" :text="'空闲'" type="success" size="mini"></u-tag>
							<u-tag v-else-if="item.parkingState==='1'" :text="'使用中'" type="error" size="mini"></u-tag>
							<u-tag v-else :text="'暂不可用'" type="error" size="mini"></u-tag>
						</div>
					</div>
					<div class="flex">
						<div class="wid">
							<text class="text-gray ellipsis">店铺：{{item.shopName}}</text>
							<view class="text-gray ellipsis">车位编码：{{item.parkingCode}}</view>
							<view class="flex-equipment">
								<view  class="text-gray ellipsis">设备编号：{{item.equipmentCode}} </view>
								<u-icon name="wifi" color="#3c9cff" v-if="item.equipmentCode && item.onlineStatus === '1' "></u-icon>
								<u-icon name="wifi-off" color="#f56c6c" v-if="item.equipmentCode && item.onlineStatus === '0'"></u-icon>
							</view>
						</div>

						<div class="flex flex-box ">
							<div class="">
								<div class="top">
									<u-button @tap="setFn('t1',item)" type="primary" shape="circle" size="mini"
										text="控制器">
									</u-button>
								</div>
								<div class="top">
									<u-button @tap="setFn('t2',item)" type="primary" shape="circle" size="mini"
										text="修改">
									</u-button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</mescroll-body>
			<u-popup :show="show.t1" :overlay="true" mode="center" :round="10" :closeOnClickOverlay='true'>
				<div class="pad ">
					<u-radio-group v-model="form.equipmentPort" placement="column">
						<u-radio :customStyle="{marginBottom: '8px'}" v-for="(item, index) in form.radiolist1"
							:key="index" :label="item.name" :name="item.value" @change="radioChange">
						</u-radio>
					</u-radio-group>
					<div class="flex pad">
						<u-button @tap="submitFn('t1')" type="primary" text="确定" size="small" shape="circle"></u-button>
						<u-button @tap="close('t1')" type="info" text="取消" size="small" shape="circle"></u-button>
					</div>
				</div>
			</u-popup>
			<u-popup :show="show.t2" :overlay="true" mode="center" :round="10" :closeOnClickOverlay='true'>
				<div class="pad text-pop">
					<div class="flex pad">
						<text>状态：</text>
						<u-radio-group v-model="form.parkingState" placement="row">
							<u-radio :customStyle="{marginRight: '16px'}" v-for="(item, index) in radiolist"
								:key="index" :label="item.name" :name="item.value">
							</u-radio>
						</u-radio-group>
					</div>
					<div class="flex">
						<text>备注：</text>
						<u--textarea v-model="form.remark" placeholder="请输入内容" maxlength='400'></u--textarea>
					</div>
					<div class="flex pad">
						<u-button @tap="updateFn('t2')" type="primary" text="确定" size="small" shape="circle">
						</u-button>
						<u-button @tap="close('t2')" type="info" text="取消" size="small" shape="circle"></u-button>
					</div>
				</div>
			</u-popup>
		</div>
	</div>
</template>

<script>
	import {
		parkingList,
		equipmentDebug,
		updateParkingStatus
	} from '@/api/parking';
	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";

	export default {
		mixins: [MescrollMixin], // 使用mixin
		comments: {},
		data() {
			return {
				radiolist: [{
						name: '空闲',
						value: 0,
						disabled: false
					}, {
						name: '进行中',
						value: 1,
						disabled: false
					},
					{
						name: '维修中',
						value: 2,
						disabled: false
					},

				],
				queryParams: {
					pageNum: 1,
					pageSize: 10
				},
				dataList: [],
				totalPage: 0,
				total: 0,
				show: {
					t1: false,
					t2: false
				},
				form: {
					// 基本案列数据
					radiolist1: [{
							name: 'DO1  通电',
							value: 'DO1',
							disabled: false
						},
						{
							name: 'DO2  断电',
							value: 'DO2',
							disabled: false
						},
						{
							name: 'DO3  开门',
							value: 'DO3',
							disabled: false
						}, {
							name: 'DO4  关门',
							value: 'DO4',
							disabled: false
						}, {
							name: 'DO5  步骤一',
							value: 'DO5',
							disabled: false
						}, {
							name: 'DO6  步骤二',
							value: 'DO6',
							disabled: false
						}, {
							name: 'DO7  步骤三',
							value: 'DO7',
							disabled: false
						}
					],
					id: null, // 请求ID
					equipmentPort: null, // 
				},
				upOption: {
					auto: false,
					empty: {
						tip: '~ 搜索暂无结果 ~'
					}
				}
			}
		},
		onLoad() {},
		onShow: function() {
			this.$nextTick(function() {
				if (this.dataList.length === 0) this.mescroll.resetUpScroll();
			})
		},
		methods: {
			setFn(val, item) {
				this.show[val] = true
				this.form.id = item.id
				this.form.equipmentId = item.equipmentId
			},
			close(val) {
				this.show[val] = false
			},
			submitFn() {
				let {
					equipmentPort,
					equipmentId
				} = this.form
				equipmentDebug(equipmentId, equipmentPort).then(res => {
					uni.showToast({
						icon: 'none',
						title: res.message,
						duration: 3000
					});
					if (res.code === 200) {
						this.close('t1')
						this.mescroll.resetUpScroll();
					}
				})
			},
			radioChange(n) {
				console.log('radioChange', n);
				this.form.equipmentPort = n
			},
			// 搜索
			searchListFn() {
				let me = this;
				me.queryParams.pageNum = 1
				me.dataList = []
				//(内部会自动page.num=1,再主动触发up.callback)
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调*/
			upCallback(page) {
				let me = this
				me.queryParams.pageNum = page.num; // 页码, 默认从1开始
				let pageSize = page.size; // 页长, 默认每页10条
				parkingList(me.queryParams).then(res => {
					let curPageData = res.data.list
					// 接口返回的当前页数据长度 (如列表有26个数据,当前页返回8个,则curPageLen=8)
					let curPageLen = curPageData.length;
					// 接口返回的总页数 (如列表有26个数据,每页10条,共3页; 则totalPage=3)
					let totalPage = res.data.totalPage
					me.total = res.data.total
					//设置列表数据
					if (page.num == 1) this.dataList = []; //如果是第一页需手动置空列表
					this.dataList = this.dataList.concat(curPageData); //追加新数据
					// 请求成功,隐藏加载状态
					//方法一(推荐): 后台接口有返回列表的总页数 totalPage
					this.mescroll.endByPage(curPageLen, totalPage);
				})
			},
			updateFn(val) {

				let {
					parkingState,
					id
				} = this.form
				updateParkingStatus(id, parkingState).then(res => {
					console.log(res)
					uni.showToast({
						icon: 'none',
						title: "修改成功",
						duration: 3000
					});
					if (res.code === 200) {
						this.close('t2')
						this.mescroll.resetUpScroll();
					}
				})
			}
		}
	}
</script>

<style>
	.top {
		margin-top: 0.3rem;
	}

	.list-item {
		width: 85%;
		margin: 0.3rem auto;
		padding: 0.5rem 1rem;
		border-radius: 0.5rem;
		box-shadow: 0 2px 14px #c7c4c4;
	}

	.u-tabs__wrapper__nav {
		align-items: center;
		/*定义body的元素垂直居中*/
		justify-content: center;
		/*定义body的里的元素水平居中*/
	}

	.u-tabs__wrapper__nav__line {
		left: 32% !important;
	}

	.flex-box {
		align-items: center;
	}

	.pop-text {
		margin: 1rem;
	}

	.wid {
		width: 70%;
	}

	.pad {
		padding: 1rem;
	}

	.flex {
		display: flex;
		justify-content: space-between;
		line-height: 1.4rem;
	}

	.first {
		max-width: 70%;
		display: inline-block;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.flex-box-item {
		width: 50%;
		text-align: left;
		display: flex;
	}

	.ellipsis {
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}
	.flex-equipment{
		text-align: left;
		display: flex;
	}

	.search-box {
		width: 86%;
		margin: 0.5rem 7% 0.3rem;
	}

	.text-pop {
		font-size: 12px;
		color: gray;
	}

	.text-gray {
		font-size: 12px;
		color: gray;
		margin: 0.2rem 0;
	}

	>>>.u-popup__content {
		width: 80%;
	}

	>>>.u-cell__title-text {
		font-size: 12px;
	}

	>>>.u-cell__body {
		padding: 3px 15px;
	}

	.text-left {
		margin-left: 0.5rem;
		color: #3d9cff;
		font-size: 12px;
		width: 3rem;
	}
</style>
