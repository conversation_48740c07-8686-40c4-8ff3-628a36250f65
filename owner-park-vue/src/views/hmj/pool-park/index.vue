<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
        <el-form-item label="车库" prop="garageId" required>
          <el-select
            v-model="queryParams.garageId"
            placeholder="请选择车库"
            style="width: 200px"
          >
            <el-option
              v-for="garage in garageList"
              :key="garage.id"
              :label="garage.pname"
              :value="garage.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="车位" prop="poolName">
          <el-input
            v-model="queryParams.poolName"
            placeholder="请输入车位"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <dict v-model="queryParams.status" code="pool_park_status" />
        </el-form-item>
        <el-form-item label="绑定的车牌号" prop="plateNums">
          <el-input
            v-model="queryParams.plateNums"
            placeholder="绑定的车牌号"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="车主姓名" prop="pname">
          <el-input
            v-model="queryParams.pname"
            placeholder="车主姓名"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item label="车主电话" prop="mobile">
          <el-input
            v-model="queryParams.mobile"
            placeholder="车主电话"
            clearable
            @keyup.enter="handleQuery()"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon>
              <Search />
            </template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon>
              <Refresh />
            </template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
          v-hasPerm="['t_pool_park_add']"
          type="success"
          @click="handleOpenBatchDialog()"
        >
          <template #icon>
            <Plus />
          </template>
          新增
        </el-button>

        <el-button
          v-hasPerm="['t_pool_park_sync']"
          type="primary"
          @click="handleSyncPoolPark()"
          v-loading="syncLoading"
        >
          <template #icon>
            <Refresh />
          </template>
          同步车位信息
        </el-button>
        <el-button
          v-hasPerm="['t_pool_park_del']"
          type="danger"
          :disabled="removeIds.length === 0"
          @click="handleDelete()"
        >
          <template #icon>
            <Delete />
          </template>
          删除
        </el-button>
      </template>

      <el-table
        ref="dataTableRef"
        v-loading="loading"
        :data="pageData"
        highlight-current-row
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="
            (index) => (queryParams.current - 1) * queryParams.size + index + 1
          "
        />
        <el-table-column
          key="poolName"
          label="车位池"
          prop="poolName"
          min-width="150"
          align="center"
        />

        <el-table-column
          key="parkName"
          label="车位名称/车位号"
          prop="parkName"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="status"
          label="状态"
          prop="payStatus"
          min-width="150"
          align="center"
        >
          <template #default="scope">
            <DictLabel
              code="pool_park_status"
              v-model="scope.row.status"
              size="small"
            />
          </template>
        </el-table-column>
        <el-table-column
          key="beginDate"
          label="有效期"
          min-width="200"
          align="center"
        >
          <template #default="{ row }">
            <el-text v-if="row.expireStatus === 1" type="warning">
              {{ row.beginDateStr }} ~ {{ row.endDateStr }}
            </el-text>
            <el-text v-else-if="row.expireStatus === 2" type="danger">
              {{ row.beginDateStr }} ~ {{ row.endDateStr }}
            </el-text>
            <el-text v-else>
              {{ row.beginDateStr }} ~ {{ row.endDateStr }}
            </el-text>
          </template>
        </el-table-column>

        <el-table-column
          key="plateNums"
          label="绑定的车牌号"
          prop="plateNums"
          min-width="150"
          align="center"
        />

        <el-table-column
          key="pname"
          label="车主姓名"
          prop="pname"
          min-width="150"
          align="center"
        />
        <el-table-column
          key="mobile"
          label="车主电话"
          prop="mobile"
          min-width="150"
          align="center"
        />

        <el-table-column fixed="right" label="操作" width="300">
          <template #default="scope">
            <el-button
              v-hasPerm="['t_pool_park_edit']"
              type="primary"
              size="small"
              link
              @click="handleOpenDialog(scope.row.id)"
            >
              <template #icon>
                <Edit />
              </template>
              编辑
            </el-button>
            <el-button
              v-hasPerm="['t_pool_park_edit']"
              type="warning"
              size="small"
              link
              v-if="scope.row.status === '1'"
              @click="handleOpenDelayDialog(scope.row)"
            >
              <template #icon>
                <Clock />
              </template>
              延期
            </el-button>
            <el-button
              v-hasPerm="['t_pool_park_del']"
              type="danger"
              size="small"
              link
              @click="handleDelete(scope.row.id)"
            >
              <template #icon>
                <Delete />
              </template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="total > 0"
        v-model:total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="handleQuery()"
      />
    </el-card>

    <!-- 车位信息表单弹窗 -->
    <el-dialog
      v-model="dialog.visible"
      :title="dialog.title"
      width="500px"
      @close="handleCloseDialog"
    >
      <el-form
        ref="dataFormRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="车位池" prop="poolName">
          <el-input
            v-model="formData.poolName"
            placeholder="车位池"
            @input="handlePoolNameChange"
          />
        </el-form-item>
        <el-form-item label="车位名称" prop="parkName">
          <el-input
            v-model="formData.parkName"
            placeholder="车位名称"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <dict v-model="formData.status" code="pool_park_status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量添加车位弹窗 -->
    <el-dialog
      v-model="batchDialog.visible"
      title="批量添加车位"
      width="600px"
      @close="handleCloseBatchDialog"
    >
      <el-form
        ref="batchFormRef"
        :model="batchFormData"
        :rules="batchRules"
        label-width="120px"
      >
        <el-form-item label="车位列表" prop="parkNames">
          <el-input
            v-model="batchFormData.parkNames"
            type="textarea"
            :rows="8"
            placeholder="请输入车位，支持逗号分隔或换行分隔&#10;例如：&#10;A0-01,A0-02,A0-03&#10;或&#10;A0-01&#10;A0-02&#10;A0-03"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <dict v-model="batchFormData.status" code="pool_park_status" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleBatchSubmit()" :loading="batchLoading">确定</el-button>
          <el-button @click="handleCloseBatchDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 车位延期弹窗 -->
    <el-dialog
      v-model="delayDialog.visible"
      title="车位延期"
      width="500px"
      @close="handleCloseDelayDialog"
    >
      <el-form
        ref="delayFormRef"
        :model="delayFormData"
        :rules="delayRules"
        label-width="120px"
      >
        <el-form-item label="车位池名称" prop="poolName">
          <el-input v-model="delayFormData.poolName" disabled />
        </el-form-item>
        <el-form-item label="车位名称" prop="parkName">
          <el-input v-model="delayFormData.parkName" disabled />
        </el-form-item>
        <el-form-item label="当前到期时间">
          <el-input :value="currentEndDateStr" disabled />
        </el-form-item>
        <el-form-item label="延期到期时间" prop="endDate">
          <el-date-picker
            v-model="delayEndDate"
            type="datetime"
            placeholder="选择延期到期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="收费金额" prop="chargeMoney">
          <el-input-number
            v-model="delayFormData.chargeMoney"
            :min="0"
            :precision="2"
            placeholder="请输入收费金额"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleDelaySubmit()" :loading="delayLoading">确定延期</el-button>
          <el-button @click="handleCloseDelayDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Eleme, Clock } from '@element-plus/icons-vue'

defineOptions({
  name: "PoolPark",
  inheritAttrs: false,
});

import PoolParkAPI, {
  PoolParkPageVO,
  PoolParkForm,
  PoolParkPageQuery,
  ParkDelayForm,
  BatchAddPoolParkForm,
} from "@/api/hmj/pool-park";
import ParkingLotAPI, { GarageOption } from "@/api/hmj/parking-lot";

const queryFormRef = ref(ElForm);
const dataFormRef = ref(ElForm);
const delayFormRef = ref(ElForm);
const batchFormRef = ref(ElForm);

const loading = ref(false);
const delayLoading = ref(false);
const batchLoading = ref(false);
const removeIds = ref<number[]>([]);
const total = ref(0);

// 车库列表
const garageList = ref<GarageOption[]>([]);

const queryParams = reactive<PoolParkPageQuery>({
  current: 1,
  size: 10,
});

// 车位信息表格数据
const pageData = ref<PoolParkPageVO[]>([]);

// 弹窗
const dialog = reactive({
  title: "",
  visible: false,
});

// 车位信息表单数据
const formData = reactive<PoolParkForm>({});

// 批量添加弹窗
const batchDialog = reactive({
  visible: false,
});

// 批量添加表单数据
const batchFormData = reactive<BatchAddPoolParkForm>({
  garageId: 0,
  poolName: "",
  parkNames: "",
  status: "0", // 默认为空闲状态
});

// 延期弹窗
const delayDialog = reactive({
  visible: false,
});

// 延期表单数据
const delayFormData = reactive<ParkDelayForm>({
  poolName: "",
  parkName: "",
  endDate: 0,
  chargeMoney: 0,
});

// 延期日期选择器的值
const delayEndDate = ref("");
// 当前到期时间显示
const currentEndDateStr = ref("");

// 车位信息表单校验规则
const rules = reactive({
  poolName: [{ required: true, message: "请输入车位池", trigger: "blur" }],
  parkName: [
    { required: true, message: "请输入车位名称、车位号", trigger: "blur" },
  ],
});

// 批量添加表单校验规则
const batchRules = reactive({
  poolName: [{ required: true, message: "请输入车位池名称", trigger: "blur" }],
  parkNames: [
    { required: true, message: "请输入车位号列表", trigger: "blur" },
  ],
});

// 延期表单校验规则
const delayRules = reactive({
  endDate: [
    { required: true, message: "请选择延期到期时间", trigger: "change" },
  ],
  chargeMoney: [
    { required: true, message: "请输入收费金额", trigger: "blur" },
    { type: "number" as const, min: 0, message: "收费金额不能小于0", trigger: "blur" },
  ],
});

/** 查询车位信息 */
function handleQuery() {
  // 检查是否选择了车库
  if (!queryParams.garageId) {
    ElMessage.warning('请先选择车库');
    return;
  }

  loading.value = true;
  PoolParkAPI.getPage(queryParams)
    .then((data) => {
      pageData.value = data.records;
      total.value = data.total;
    })
    .finally(() => {
      loading.value = false;
    });
}

/** 重置车位信息查询 */
function handleResetQuery() {
  queryFormRef.value!.resetFields();
  queryParams.current = 1;
  handleQuery();
}

/** 行复选框选中记录选中ID集合 */
function handleSelectionChange(selection: any) {
  removeIds.value = selection.map((item: any) => item.id);
}

/** 打开车位信息弹窗 */
function handleOpenDialog(id?: number) {
  dialog.visible = true;
  if (id) {
    dialog.title = "修改车位信息";
    PoolParkAPI.getFormData(id).then((data) => {
      Object.assign(formData, data);
    });
  } else {
    dialog.title = "新增车位信息";
    // 新增时设置当前选中的车库ID
    if (queryParams.garageId) {
      formData.garageId = queryParams.garageId;
    }
  }
}

/** 提交车位信息表单 */
function handleSubmit() {
  dataFormRef.value.validate((valid: any) => {
    if (valid) {
      // 确保提交时带上车库ID
      if (!formData.garageId && queryParams.garageId) {
        formData.garageId = queryParams.garageId;
      }

      loading.value = true;
      const id = formData.id;
      if (id) {
        PoolParkAPI.update(formData)
          .then(() => {
            ElMessage.success("修改成功");
            handleCloseDialog();
            handleQuery(); // 保持当前车库选择，不重置查询条件
          })
          .finally(() => (loading.value = false));
      } else {
        PoolParkAPI.add(formData)
          .then(() => {
            ElMessage.success("新增成功");
            handleCloseDialog();
            handleQuery(); // 保持当前车库选择，不重置查询条件
          })
          .finally(() => (loading.value = false));
      }
    }
  });
}

/** 车位池名称变化时自动生成车位号 */
function handlePoolNameChange() {
  if (formData.poolName && formData.poolName.trim()) {
    const poolName = formData.poolName.trim();

    // 新增时自动生成车位号
    if (!formData.id) {
      // 如果车位名称为空，自动生成
      if (!formData.parkName || formData.parkName.trim() === '') {
        formData.parkName = `${poolName}`;
      }
      // 如果车位名称看起来是之前自动生成的格式，则更新
      else if (formData.parkName.match(/^.+-\d{3}$/)) {
        formData.parkName = `${poolName}`;
      }
    }
    // 修改时允许用户自由编辑，不自动更改车位名称
  }
}

/** 关闭车位信息弹窗 */
function handleCloseDialog() {
  dialog.visible = false;
  dataFormRef.value.resetFields();
  dataFormRef.value.clearValidate();
  formData.id = undefined;
  formData.garageId = undefined;
}

/** 打开批量添加车位弹窗 */
function handleOpenBatchDialog() {
  // 检查是否选择了车库
  if (!queryParams.garageId) {
    ElMessage.warning('请先选择车库');
    return;
  }

  batchDialog.visible = true;
  batchFormData.garageId = queryParams.garageId;
  batchFormData.poolName = "";
  batchFormData.parkNames = "";
  batchFormData.status = "0";
}

/** 关闭批量添加弹窗 */
function handleCloseBatchDialog() {
  batchDialog.visible = false;
  batchFormRef.value?.resetFields();
  batchFormRef.value?.clearValidate();
}

/** 提交批量添加表单 */
function handleBatchSubmit() {
  batchFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      batchLoading.value = true;
      PoolParkAPI.batchAdd(batchFormData)
        .then((response) => {
          ElMessage.success(response.data || "批量添加成功");
          handleCloseBatchDialog();
          handleQuery(); // 刷新列表
        })
        .catch((error) => {
          ElMessage.error(error.message || "批量添加失败");
        })
        .finally(() => {
          batchLoading.value = false;
        });
    }
  });
}

/** 删除车位信息 */
function handleDelete(id?: number) {
  const ids = [id || removeIds.value].join(",");
  if (!ids) {
    ElMessage.warning("请勾选删除项");
    return;
  }

  ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
      loading.value = true;
      PoolParkAPI.deleteByIds(ids)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    () => {
      ElMessage.info("已取消删除");
    }
  );
}
const  syncLoading = ref(false)
function handleSyncPoolPark() {
  // 检查是否选择了车库
  if (!queryParams.garageId) {
    ElMessage.warning("请先选择车库");
    return;
  }

  ElMessageBox.confirm("确认同步数据?", "警告", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    () => {
  syncLoading.value = true;
  PoolParkAPI.syncPoolPark(queryParams.garageId)
    .then(() => {
      ElMessage.success("同步成功");
      // 选择车库后进行查询
      handleQuery();
    })
    .finally(() => (syncLoading.value = false));
  },
    () => {
      ElMessage.info("已取消同步");
    }
  );
}

/** 打开车位延期弹窗 */
function handleOpenDelayDialog(row: PoolParkPageVO) {
  delayDialog.visible = true;
  delayFormData.poolName = row.poolName || "";
  delayFormData.parkName = row.parkName || "";
  delayFormData.garageId = row.garageId || 0;

  // 显示当前到期时间
  if (row.endDate) {
    const currentEndDate = new Date(row.endDate * 1000);
    currentEndDateStr.value = currentEndDate.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  // 重置表单数据
  delayEndDate.value = "";
  delayFormData.chargeMoney = 0;
  delayFormData.beginDate = row.beginDate
}

/** 关闭车位延期弹窗 */
function handleCloseDelayDialog() {
  delayDialog.visible = false;
  delayFormRef.value?.resetFields();
  delayFormRef.value?.clearValidate();
  delayEndDate.value = "";
  currentEndDateStr.value = "";
}

/** 提交车位延期 */
function handleDelaySubmit() {
  delayFormRef.value?.validate((valid: boolean) => {
    if (valid) {
      // 将选择的日期时间转换为时间戳（秒）
      if (delayEndDate.value) {
        delayFormData.endDate = Math.floor(new Date(delayEndDate.value).getTime() / 1000);
      }

      delayLoading.value = true;
      PoolParkAPI.parkDelay(delayFormData)
        .then(() => {
          ElMessage.success("车位延期成功");
          handleCloseDelayDialog();
          // 选择车库后进行查询
          handleQuery();
        })
        .catch((error) => {
          ElMessage.error(error.message || "延期失败");
        })
        .finally(() => {
          delayLoading.value = false;
        });
    }
  });
}

onMounted(() => {
  // 先加载车库列表，然后在回调中进行查询
  loadGarageListAndQuery();
});

/** 加载车库列表并查询 */
function loadGarageListAndQuery() {
  ParkingLotAPI.getGarageList()
    .then((data) => {
      garageList.value = data || [];
      // 默认选择第一个车库
      if (garageList.value.length > 0) {
        queryParams.garageId = garageList.value[0].id;
        // 选择车库后进行查询
        handleQuery();
      } else {
        ElMessage.warning('暂无可用车库');
      }
    })
    .catch((error) => {
      console.error('获取车库列表失败:', error);
      ElMessage.error('获取车库列表失败');
    });
}
</script>
