<template>
  <div class="app-container">
    <div class="search-container">
      <el-form ref="queryFormRef" :model="queryParams" :inline="true">
                <el-form-item label="停车场名称" prop="pname">
                      <el-input
                          v-model="queryParams.pname"
                          placeholder="停车场名称"
                          clearable
                          @keyup.enter="handleQuery()"
                      />
                </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">
            <template #icon><Search /></template>
            搜索
          </el-button>
          <el-button @click="handleResetQuery">
            <template #icon><Refresh /></template>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-card shadow="never" class="table-container">
      <template #header>
        <el-button
            v-hasPerm="['t_parking_lot_add']"
            type="success"
            @click="handleOpenDialog()"
        >
          <template #icon><Plus /></template>
          新增
        </el-button>
        <el-button
            v-hasPerm="['t_parking_lot_del']"
            type="danger"
            :disabled="removeIds.length === 0"
            @click="handleDelete()"
        >
          <template #icon><Delete /></template>
          删除
        </el-button>
      </template>

      <el-table
          ref="dataTableRef"
          v-loading="loading"
          :data="pageData"
          highlight-current-row
          border
          @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
                <el-table-column
                    key="id"
                    label="停车场ID"
                    prop="id"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="pname"
                    label="停车场名称"
                    prop="pname"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="parea"
                    label="所在区"
                    prop="parea"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="city"
                    label="城市"
                    prop="city"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="address"
                    label="详细地址"
                    prop="address"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="lng"
                    label="经度"
                    prop="lng"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="lat"
                    label="纬度"
                    prop="lat"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="parkTotalCount"
                    label="总车位数量"
                    prop="parkTotalCount"
                    min-width="150"
                    align="center"
                />
                <el-table-column
                    key="commKey"
                    label="项目唯一编码"
                    prop="commKey"
                    min-width="150"
                    align="center"
                />
        <el-table-column fixed="right" label="操作" width="320">
          <template #default="scope">
            <el-button
                v-hasPerm="['t_parking_lot_edit']"
                type="primary"
                size="small"
                link
                @click="handleOpenDialog(scope.row.id)"
            >
              <template #icon><Edit /></template>
              编辑
            </el-button>
            <el-button
                type="success"
                size="small"
                link
                @click="handleDownloadQrcode(scope.row)"
                :loading="qrcodeLoading === scope.row.id"
            >
              <template #icon><Download /></template>
              下载二维码
            </el-button>
            <el-button
                v-hasPerm="['t_parking_lot_del']"
                type="danger"
                size="small"
                link
                @click="handleDelete(scope.row.id)"
            >
              <template #icon><Delete /></template>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-if="total > 0"
          v-model:total="total"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="handleQuery()"
      />
    </el-card>

    <!-- 停车场信息表单弹窗 -->
    <el-dialog
        v-model="dialog.visible"
        :title="dialog.title"
        width="500px"
        @close="handleCloseDialog"
    >
      <el-form ref="dataFormRef" :model="formData" :rules="rules" label-width="100px">
                <el-form-item label="停车场ID" prop="id">
                      <el-input
                          v-model="formData.id"
                          placeholder="停车场ID"
                      />
                </el-form-item>
                <el-form-item label="停车场名称" prop="pname">
                      <el-input
                          v-model="formData.pname"
                          placeholder="停车场名称"
                      />
                </el-form-item>
                <el-form-item label="所在区" prop="parea">
                      <el-input
                          v-model="formData.parea"
                          placeholder="所在区"
                      />
                </el-form-item>
                <el-form-item label="城市" prop="city">
                      <el-input
                          v-model="formData.city"
                          placeholder="城市"
                      />
                </el-form-item>
                <el-form-item label="详细地址" prop="address">
                      <el-input
                          v-model="formData.address"
                          placeholder="详细地址"
                      />
                </el-form-item>
                <el-form-item label="经度" prop="lng">
                      <el-input
                          v-model="formData.lng"
                          placeholder="经度"
                      />
                </el-form-item>
                <el-form-item label="纬度" prop="lat">
                      <el-input
                          v-model="formData.lat"
                          placeholder="纬度"
                      />
                </el-form-item>
                <el-form-item label="总车位数量" prop="parkTotalCount">
                      <el-input
                          v-model="formData.parkTotalCount"
                          placeholder="总车位数量"
                      />
                </el-form-item>
                <el-form-item label="唯一编码" prop="commKey">
                      <el-input
                          v-model="formData.commKey"
                          placeholder="项目唯一编码"
                      />
                </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleSubmit()">确定</el-button>
          <el-button @click="handleCloseDialog()">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
  defineOptions({
    name: "ParkingLot",
    inheritAttrs: false,
  });

  import ParkingLotAPI, { ParkingLotPageVO, ParkingLotForm, ParkingLotPageQuery } from "@/api/hmj/parking-lot";

  const queryFormRef = ref(ElForm);
  const dataFormRef = ref(ElForm);

  const loading = ref(false);
  const qrcodeLoading = ref<number | null>(null);
  const removeIds = ref<number[]>([]);
  const total = ref(0);

  const queryParams = reactive<ParkingLotPageQuery>({
    current: 1,
    size: 10,
  });

  // 停车场信息表格数据
  const pageData = ref<ParkingLotPageVO[]>([]);

  // 弹窗
  const dialog = reactive({
    title: "",
    visible: false,
  });

  // 停车场信息表单数据
  const formData = reactive<ParkingLotForm>({});

  // 停车场信息表单校验规则
  const rules = reactive({
                      pname: [{ required: true, message: "请输入停车场名称", trigger: "blur" }],
                      parea: [{ required: true, message: "请输入所在区", trigger: "blur" }],
                      city: [{ required: true, message: "请输入城市", trigger: "blur" }],
                      address: [{ required: true, message: "请输入详细地址", trigger: "blur" }],
                      lng: [{ required: true, message: "请输入经度", trigger: "blur" }],
                      lat: [{ required: true, message: "请输入纬度", trigger: "blur" }],
                      parkTotalCount: [{ required: true, message: "请输入总车位数量", trigger: "blur" }],
                      parkEmptyCount: [{ required: true, message: "请输入空车位数量", trigger: "blur" }],
                      parkEmptyUpdateDate: [{ required: true, message: "请输入空车位信息更新时间戳", trigger: "blur" }],
                      isOnline: [{ required: true, message: "请输入是否在线（1：在线，0：离线）", trigger: "blur" }],
                      outDelay: [{ required: true, message: "请输入出场延迟时间（分钟）", trigger: "blur" }],
                      commKey: [{ required: true, message: "请输入项目唯一编码", trigger: "blur" }],
  });

  /** 查询停车场信息 */
  function handleQuery() {
    loading.value = true;
          ParkingLotAPI.getPage(queryParams)
        .then((data) => {
          pageData.value = data.records;
          total.value = data.total;
        })
        .finally(() => {
          loading.value = false;
        });
  }

  /** 重置停车场信息查询 */
  function handleResetQuery() {
    queryFormRef.value!.resetFields();
    queryParams.current = 1;
    handleQuery();
  }

  /** 行复选框选中记录选中ID集合 */
  function handleSelectionChange(selection: any) {
    removeIds.value = selection.map((item: any) => item.id);
  }

  /** 打开停车场信息弹窗 */
  function handleOpenDialog(id?: number) {
    dialog.visible = true;
    if (id) {
      dialog.title = "修改停车场信息";
            ParkingLotAPI.getFormData(id).then((data) => {
        Object.assign(formData, data);
      });
    } else {
      dialog.title = "新增停车场信息";
    }
  }

  /** 提交停车场信息表单 */
  function handleSubmit() {
    dataFormRef.value.validate((valid: any) => {
      if (valid) {
        loading.value = true;
        const id = formData.id;
        if (id) {
                ParkingLotAPI.update(formData)
              .then(() => {
                ElMessage.success("修改成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        } else {
                ParkingLotAPI.add(formData)
              .then(() => {
                ElMessage.success("新增成功");
                handleCloseDialog();
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        }
      }
    });
  }

  /** 关闭停车场信息弹窗 */
  function handleCloseDialog() {
    dialog.visible = false;
    dataFormRef.value.resetFields();
    dataFormRef.value.clearValidate();
    formData.id = undefined;
  }

  /** 删除停车场信息 */
  function handleDelete(id?: number) {
    const ids = [id || removeIds.value].join(",");
    if (!ids) {
      ElMessage.warning("请勾选删除项");
      return;
    }

    ElMessageBox.confirm("确认删除已选中的数据项?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    }).then(
        () => {
          loading.value = true;
                ParkingLotAPI.deleteByIds(ids)
              .then(() => {
                ElMessage.success("删除成功");
                handleResetQuery();
              })
              .finally(() => (loading.value = false));
        },
        () => {
          ElMessage.info("已取消删除");
        }
    );
  }

  /** 下载车库二维码 */
  function handleDownloadQrcode(row: any) {
    if (!row.id) {
      ElMessage.error("车库ID不能为空");
      return;
    }

    qrcodeLoading.value = row.id;

    ParkingLotAPI.getGarageQrcode(row.id)
      .then((base64Data: string) => {
        if (!base64Data) {
          ElMessage.error("二维码数据为空");
          return;
        }

        // 构造完整的base64图片URL
        const imgUrl = "data:image/png;base64," + base64Data;
        const imageName = `${row.pname || '车库'}_二维码`;

        // 如果浏览器支持msSaveOrOpenBlob方法（也就是使用IE浏览器的时候），那么调用该方法去下载图片
        if ((window.navigator as any).msSaveOrOpenBlob) {
          const bstr = atob(imgUrl.split(',')[1]);
          let n = bstr.length;
          const u8arr = new Uint8Array(n);
          while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
          }
          const blob = new Blob([u8arr]);
          (window.navigator as any).msSaveOrOpenBlob(blob, imageName + '.png');
        } else {
          // 这里就按照chrome等新版浏览器来处理
          const a = document.createElement('a');
          a.href = imgUrl;
          a.setAttribute('target', 'HTEPreview');
          a.setAttribute('download', imageName + '.png');
          a.click();
        }

        ElMessage.success("二维码下载成功");
      })
      .catch((error) => {
        console.error('下载二维码失败:', error);
        ElMessage.error("下载二维码失败，请稍后重试");
      })
      .finally(() => {
        qrcodeLoading.value = null;
      });
  }

  onMounted(() => {
    handleQuery();
  });
</script>
