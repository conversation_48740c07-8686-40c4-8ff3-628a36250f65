<template>
  <div class="dashboard-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <h1 class="banner-title">
          <el-icon class="banner-icon"><House /></el-icon>
          好民居自助停车管理系统
        </h1>
        <p class="banner-subtitle">智能化停车管理，让停车更简单</p>
      </div>
      <div class="banner-decoration">
        <el-icon class="decoration-icon"><CaretRight /></el-icon>
      </div>
    </div>
    <!-- 统计概览 -->
    <el-row :gutter="24" class="stats-row">
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon parking">
            <el-icon><Location /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">-</div>
            <div class="stat-label">总车位数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon occupied">
            <el-icon><CaretRight /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">-</div>
            <div class="stat-label">已占用</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon available">
            <el-icon><CircleCheck /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">-</div>
            <div class="stat-label">可用车位</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon revenue">
            <el-icon><Money /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-number">¥-</div>
            <div class="stat-label">今日收入</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <!-- 系统介绍 -->
        <el-col :xs="24" :sm="12" :lg="8">
          <el-card class="info-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><InfoFilled /></el-icon>
                <span>系统介绍</span>
              </div>
            </template>
            <div class="card-content">
              <p>好民居自助停车管理系统是一套完整的智能停车解决方案，提供：</p>
              <ul class="feature-list">
                <li><el-icon><Check /></el-icon>车位实时监控</li>
                <li><el-icon><Check /></el-icon>自助缴费服务</li>
                <li><el-icon><Check /></el-icon>车辆进出管理</li>
                <li><el-icon><Check /></el-icon>数据统计分析</li>
              </ul>
            </div>
          </el-card>
        </el-col>

        <!-- 功能模块 -->
        <el-col :xs="24" :sm="12" :lg="8">
          <el-card class="info-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><Grid /></el-icon>
                <span>主要功能</span>
              </div>
            </template>
            <div class="card-content">
              <div class="function-grid">
                <div class="function-item">
                  <el-icon class="function-icon"><Monitor /></el-icon>
                  <span>停车监控</span>
                </div>
                <div class="function-item">
                  <el-icon class="function-icon"><CreditCard /></el-icon>
                  <span>收费管理</span>
                </div>
                <div class="function-item">
                  <el-icon class="function-icon"><User /></el-icon>
                  <span>用户管理</span>
                </div>
                <div class="function-item">
                  <el-icon class="function-icon"><DataAnalysis /></el-icon>
                  <span>数据分析</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 帮助指南 -->
        <el-col :xs="24" :sm="12" :lg="8">
          <el-card class="info-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <el-icon class="header-icon"><QuestionFilled /></el-icon>
                <span>帮助指南</span>
              </div>
            </template>
            <div class="card-content">
              <div class="help-item">
                <el-icon class="help-icon"><Document /></el-icon>
                <div class="help-text">
                  <h4>快速入门</h4>
                  <p>了解系统基本操作流程</p>
                </div>
              </div>
              <div class="help-item">
                <el-icon class="help-icon"><Setting /></el-icon>
                <div class="help-text">
                  <h4>系统配置</h4>
                  <p>设置停车场基本参数</p>
                </div>
              </div>
              <div class="help-item">
                <el-icon class="help-icon"><Phone /></el-icon>
                <div class="help-text">
                  <h4>技术支持</h4>
                  <p>联系客服获取帮助</p>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

    </div>
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: "Dashboard",
  inheritAttrs: false,
});
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 84px);
}

// 欢迎横幅
.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);

  .banner-content {
    position: relative;
    z-index: 2;
  }

  .banner-title {
    font-size: 32px;
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 12px;

    .banner-icon {
      font-size: 36px;
      color: #ffd700;
    }
  }

  .banner-subtitle {
    font-size: 18px;
    margin: 0;
    opacity: 0.9;
  }

  .banner-decoration {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.1;

    .decoration-icon {
      font-size: 120px;
    }
  }
}

// 主要内容区域
.main-content {
  .info-card {
    height: 100%;
    border-radius: 12px;
    border: none;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      color: #333;

      .header-icon {
        font-size: 20px;
        color: var(--el-color-primary);
      }
    }

    .card-content {
      color: #666;
      line-height: 1.6;

      p {
        margin-bottom: 16px;
      }

      .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          color: #555;

          .el-icon {
            color: #52c41a;
            font-size: 16px;
          }
        }
      }
    }
  }
}

// 功能网格
.function-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;

  .function-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      background: var(--el-color-primary-light-9);
      transform: translateY(-2px);
    }

    .function-icon {
      font-size: 32px;
      color: var(--el-color-primary);
      margin-bottom: 8px;
    }

    span {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
  }
}

// 帮助项目
.help-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 20px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:hover {
    background: #f8f9fa;
  }

  .help-icon {
    font-size: 24px;
    color: var(--el-color-primary);
    margin-top: 4px;
  }

  .help-text {
    h4 {
      margin: 0 0 4px 0;
      color: #333;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
}

// 统计卡片行
.stats-row {
  margin: 32px 0;

  .stat-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    height: 100px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    }

    .stat-icon {
      width: 52px;
      height: 52px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;

      &.parking {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.occupied {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.available {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }

      &.revenue {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
      }
    }

    .stat-content {
      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #333;
        line-height: 1;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
      }
    }
  }
}

// 联系卡片
.contact-card {
  margin-top: 32px;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  border: none;

  :deep(.el-card__body) {
    padding: 32px;
  }

  .contact-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 24px;

    .contact-info {
      h3 {
        margin: 0 0 8px 0;
        color: #333;
        font-size: 24px;
        font-weight: 600;
      }

      p {
        margin: 0;
        color: #666;
        font-size: 16px;
      }
    }

    .contact-methods {
      display: flex;
      gap: 32px;
      flex-wrap: wrap;

      .contact-item {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #333;
        font-weight: 500;

        .el-icon {
          font-size: 20px;
          color: #ff6b35;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .welcome-banner {
    padding: 24px;
    text-align: center;

    .banner-title {
      font-size: 24px;
      justify-content: center;
    }

    .banner-subtitle {
      font-size: 16px;
    }

    .banner-decoration {
      display: none;
    }
  }

  .function-grid {
    grid-template-columns: 1fr;
  }

  .contact-content {
    flex-direction: column;
    text-align: center;

    .contact-methods {
      justify-content: center;
      gap: 16px;

      .contact-item {
        flex-direction: column;
        gap: 4px;
      }
    }
  }

  .stats-row {
    .stat-card {
      padding: 16px;
      height: auto;
      flex-direction: column;
      text-align: center;
      gap: 12px;

      .stat-icon {
        width: 48px;
        height: 48px;
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
        }
      }
    }
  }
}
</style>
