{"name": "好民居自助停车管理系统", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit & vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint  --fix --ext .ts,.js,.vue ./src ", "lint:prettier": "prettier --write \"**/*.{js,cjs,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint  \"**/*.{css,scss,vue}\" --fix", "lint:lint-staged": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "commit": "git-cz"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{cjs,json}": ["prettier --write"], "*.{vue,html}": ["eslint --fix", "prettier --write", "stylelint --fix"], "*.{scss,css}": ["stylelint --fix", "prettier --write"], "*.md": ["prettier --write"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^10.11.1", "animate.css": "^4.1.1", "axios": "^1.7.4", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "element-plus": "2.8.7", "nprogress": "^0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.2", "pinia": "^2.2.6", "qs": "^6.13.0", "sortablejs": "^1.15.3", "vue": "^3.4.38", "vue-i18n": "10.0.5", "vue-router": "^4.4.3"}, "devDependencies": {"@commitlint/cli": "^18.6.1", "@commitlint/config-conventional": "^18.6.3", "@iconify-json/ep": "^1.1.16", "@types/codemirror": "^5.60.15", "@types/node": "^20.14.15", "@types/nprogress": "^0.2.3", "@types/path-browserify": "^1.0.2", "@types/qs": "^6.9.15", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "@vitejs/plugin-vue": "^5.1.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.20", "commitizen": "^4.3.0", "cz-git": "^1.9.4", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-vue": "^9.27.0", "fast-glob": "^3.3.2", "husky": "^9.1.4", "lint-staged": "^15.2.9", "postcss": "^8.4.41", "postcss-html": "^1.7.0", "postcss-scss": "^4.0.9", "prettier": "^3.3.3", "sass": "1.79.5", "stylelint": "^16.8.2", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.1", "terser": "^5.31.6", "typescript": "^5.5.4", "unocss": "^0.63.6", "unplugin-auto-import": "^0.18.4", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.11", "vite-plugin-mock-dev-server": "^1.7.1", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.3.8", "vue-tsc": "^2.1.10"}, "repository": "https://gitee.com/harry-tech/harry-vue.git", "author": "<PERSON>技术", "license": "MIT", "engines": {"node": ">=18.0.0"}}