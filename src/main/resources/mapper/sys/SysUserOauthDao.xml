<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE  mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.harry.sys.dao.SysUserOauthDao">
	<resultMap id="sysUserOauthMap" type="cn.harry.sys.entity.SysUserOauth">
            <result column="id" property="id" />
	        <result column="user_id" property="userId" />
	        <result column="oauth_type" property="oauthType" />
	        <result column="appid" property="appid" />
	        <result column="openid" property="openid" />
	        <result column="union_id" property="unionId" />
	        <result column="create_time" property="createTime" />
	        <result column="modify_time" property="modifyTime" />
	        <result column="valid" property="valid" />
		</resultMap>
	<select id="getAdminUserOpenid" resultType="java.lang.String">
		SELECT openid FROM sys_user_oauth WHERE user_id  = 1 AND valid = 1
	</select>
</mapper>