package cn.harry.wx.controller;

import cn.harry.common.api.CommonResult;
import cn.harry.wx.form.MaterialFileDeleteForm;
import cn.harry.wx.service.WxAssetsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.material.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * 微信公众号素材管理
 * 参考官方文档：https://developers.weixin.qq.com/doc/offiaccount/Asset_Management/New_temporary_materials.html
 * 参考WxJava开发文档：https://github.com/Wechat-Group/WxJava/wiki/MP_永久素材管理
 */
@RestController
@RequestMapping("/wx/assets")
@Api(tags = {"公众号素材-管理后台"})
public class WxAssetsManageController {
    @Autowired
    WxAssetsService wxAssetsService;

    /**
     * 获取素材总数
     *
     * @return
     * @throws WxErrorException
     */
    @GetMapping("/materialCount")
    @ApiOperation(value = "文件素材总数")
    public CommonResult<WxMpMaterialCountResult> materialCount(@CookieValue String appid) throws WxErrorException {
        WxMpMaterialCountResult res = wxAssetsService.materialCount(appid);
        return CommonResult.success(res);
    }

    /**
     * 获取素材总数
     *
     * @return
     * @throws WxErrorException
     */
    @GetMapping("/materialNewsInfo")
    @ApiOperation(value = "图文素材总数")
    public CommonResult<WxMpMaterialNews> materialNewsInfo(@CookieValue String appid, String mediaId) throws WxErrorException {
        WxMpMaterialNews res = wxAssetsService.materialNewsInfo(appid, mediaId);
        return CommonResult.success(res);
    }


    /**
     * 根据类别分页获取非图文素材列表
     *
     * @param type
     * @param page
     * @return
     * @throws WxErrorException
     */
    @GetMapping("/materialFileBatchGet")
    @PreAuthorize("@ss.hasPermi('wx:assets:list')")
    @ApiOperation(value = "根据类别分页获取非图文素材列表")
    public CommonResult<WxMpMaterialFileBatchGetResult> materialFileBatchGet(@CookieValue String appid, @RequestParam(defaultValue = "image") String type,
                                                                             @RequestParam(defaultValue = "1") int page) throws WxErrorException {
        WxMpMaterialFileBatchGetResult res = wxAssetsService.materialFileBatchGet(appid, type, page);
        return CommonResult.success(res);
    }

    /**
     * 分页获取图文素材列表
     *
     * @param page
     * @return
     * @throws WxErrorException
     */
    @GetMapping("/materialNewsBatchGet")
    @PreAuthorize("@ss.hasPermi('wx:assets:list')")
    @ApiOperation(value = "分页获取图文素材列表")
    public CommonResult<WxMpMaterialNewsBatchGetResult> materialNewsBatchGet(@CookieValue String appid, @RequestParam(defaultValue = "1") int page) throws WxErrorException {
        WxMpMaterialNewsBatchGetResult res = wxAssetsService.materialNewsBatchGet(appid, page);
        return CommonResult.success(res);
    }

    /**
     * 添加图文永久素材
     *
     * @param articles
     * @return
     * @throws WxErrorException
     */
    @PostMapping("/materialNewsUpload")
    @PreAuthorize("@ss.hasPermi('wx:assets:save')")
    @ApiOperation(value = "添加图文永久素材")
    public CommonResult<WxMpMaterialUploadResult> materialNewsUpload(@CookieValue String appid, @RequestBody List<WxMpNewsArticle> articles) throws WxErrorException {
        if (articles.isEmpty()) {
            return CommonResult.failed("图文列表不得为空");
        }
        WxMpMaterialUploadResult res = wxAssetsService.materialNewsUpload(appid, articles);
        return CommonResult.success(res);
    }

    /**
     * 修改图文素材文章
     *
     * @param form
     * @return
     * @throws WxErrorException
     */
    @PostMapping("/materialArticleUpdate")
    @PreAuthorize("@ss.hasPermi('wx:assets:save')")
    @ApiOperation(value = "修改图文素材文章")
    public CommonResult materialArticleUpdate(@CookieValue String appid, @RequestBody WxMpMaterialArticleUpdate form) throws WxErrorException {
        if (form.getArticles() == null) {
            return CommonResult.failed("文章不得为空");
        }
        wxAssetsService.materialArticleUpdate(appid, form);
        return CommonResult.success();
    }

    /**
     * 添加多媒体永久素材
     *
     * @param file
     * @param fileName
     * @param mediaType
     * @return
     * @throws WxErrorException
     * @throws IOException
     */
    @PostMapping("/materialFileUpload")
    @PreAuthorize("@ss.hasPermi('wx:assets:save')")
    @ApiOperation(value = "添加多媒体永久素材")
    public CommonResult<WxMpMaterialUploadResult> materialFileUpload(@CookieValue String appid, MultipartFile file, String fileName, String mediaType) throws WxErrorException, IOException {
        if (file == null) {
            return CommonResult.failed("文件不得为空");
        }

        WxMpMaterialUploadResult res = wxAssetsService.materialFileUpload(appid, mediaType, fileName, file);
        return CommonResult.success(res);
    }

    /**
     * 删除素材
     *
     * @param form
     * @return
     * @throws WxErrorException
     */
    @PostMapping("/materialDelete")
    @PreAuthorize("@ss.hasPermi('wx:assets:remove')")
    @ApiOperation(value = "删除素材")
    public CommonResult<Boolean> materialDelete(@CookieValue String appid, @RequestBody MaterialFileDeleteForm form) throws WxErrorException {
        boolean res = wxAssetsService.materialDelete(appid, form.getMediaId());
        return CommonResult.success(res);
    }

}
