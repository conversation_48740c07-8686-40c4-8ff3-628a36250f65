package cn.harry.wx.controller;

import cn.harry.common.annotation.SysLog;
import cn.harry.common.api.CommonPage;
import cn.harry.common.api.CommonResult;
import cn.harry.common.enums.BusinessType;
import cn.harry.wx.entity.WxUser;
import cn.harry.wx.service.WxUserService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 用户表
 *
 * <AUTHOR>
 * Date 2021-04-08 16:19:27
 * Copyright (C) www.tech-harry.cn
 */
@Api(tags = "wx-wxuser => 用户表")
@RestController
@RequestMapping("wx/wxuser")
public class WxUserController {
    @Resource
    private WxUserService wxUserService;

    @ApiOperation("list => 分页获取用户表列表")
    @PreAuthorize("@ss.hasPermi('wx:wxuser:list')")
    @GetMapping(value = "/list")
    public CommonResult<CommonPage<WxUser>> list(@CookieValue String appid,WxUser wxUser,
                                                  @RequestParam(value = "pageSize", defaultValue = "5") Integer pageSize,
                                                  @RequestParam(value = "pageNum", defaultValue = "1") Integer pageNum) {
        wxUser.setAppid(appid);
        IPage<WxUser> page = wxUserService.getPage(wxUser, pageSize, pageNum);
        return CommonResult.success(CommonPage.restPage(page));
    }

    /**
     * 查询用户表详细
     */
    @ApiOperation("{id} => 获取用户表信息")
    @PreAuthorize("@ss.hasPermi('wx:wxuser:query')")
    @GetMapping(value = "/{id}")
    public CommonResult<WxUser> getInfo(@PathVariable Long id) {
        return CommonResult.success(wxUserService.selectById(id));
    }

    @ApiOperation("create => 新建用户表")
    @PostMapping(value = "/create")
    public CommonResult<Integer> create(@RequestBody WxUser wxUser) {
        int count = wxUserService.create(wxUser);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    @ApiOperation("update/{id} => 修改指定用户表")
    @PreAuthorize("@ss.hasPermi('wx:wxuser:edit')")
    @SysLog(title = "用户表数据", businessType = BusinessType.UPDATE)
    @PutMapping(value = "/update/{id}")
    public CommonResult<Integer> update(@PathVariable Long id, @RequestBody WxUser wxUser) {
        int count = wxUserService.update(id, wxUser);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }


    @ApiOperation("deleteByIds/{ids} => 删除指定用户表")
    @PreAuthorize("@ss.hasPermi('wx:wxuser:remove')")
    @SysLog(title = "用户表类型", businessType = BusinessType.DELETE)
    @DeleteMapping(value = "/deleteByIds/{ids}")
    public CommonResult<Integer> deleteByIds(@PathVariable String[] ids) {
        int count =wxUserService.deleteByIds(ids);
        if (count > 0) {
            return CommonResult.success(count);
        }
        return CommonResult.failed();
    }

    /**
     * 同步用户列表
     */
    @PostMapping("/syncWxUsers")
    @PreAuthorize("@ss.hasPermi('wx:wxuser:edit')")
    @ApiOperation(value = "同步用户列表到数据库")
    public CommonResult syncWxUsers(@CookieValue String appid) {
        wxUserService.syncWxUsers(appid);

        return CommonResult.success("任务已建立");
    }

}
